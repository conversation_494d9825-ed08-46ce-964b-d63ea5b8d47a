<template>
  <Dialog :open="isOpen" @update:open="updateOpen">
    <DialogContent class="sm:max-w-[80vw] max-h-[80vh] flex flex-col">
      <DialogHeader>
        <DialogTitle class="flex items-center text-xl">
          <FileText class="w-5 h-5 mr-2 text-blue-500" />
          <span>训练日志详情</span>
          <Badge v-if="logLines.length" class="ml-2 bg-blue-500" size="sm">
            {{ logLines.length }} 行
          </Badge>
        </DialogTitle>
        <DialogDescription>任务ID: {{ taskId }}</DialogDescription>
      </DialogHeader>

      <div class="flex items-center gap-2 mb-2">
        <div class="flex-1">
          <Input v-model="searchQuery" placeholder="搜索日志内容..." class="w-full">
            <template #prefix>
              <Search class="h-4 w-4 text-muted-foreground" />
            </template>
          </Input>
        </div>
        <Select v-model="filterType">
          <SelectTrigger class="w-[180px]">
            <SelectValue placeholder="筛选类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部日志</SelectItem>
            <SelectItem value="info">信息记录</SelectItem>
            <SelectItem value="error">错误记录</SelectItem>
            <SelectItem value="epoch">训练轮次</SelectItem>
          </SelectContent>
        </Select>
        <div class="flex gap-1">
          <Button
            variant="outline"
            size="icon"
            class="h-9 w-9"
            :disabled="!logContent"
            @click="handleRefresh"
          >
            <RefreshCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            class="h-9 w-9"
            :disabled="!logContent"
            @click="handleExport"
          >
            <Download class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div class="flex-1 overflow-hidden border rounded-md bg-muted/20 dark:bg-slate-950">
        <div ref="logContainer" class="h-full overflow-auto code-container" @scroll="handleScroll">
          <div v-if="isLoading" class="flex items-center justify-center h-full p-4">
            <Loader2 class="w-8 h-8 animate-spin text-blue-500" />
            <span class="ml-2 text-muted-foreground">加载日志中...</span>
          </div>
          <div
            v-else-if="!logContent"
            class="flex flex-col items-center justify-center h-full p-4 text-muted-foreground"
          >
            <FileQuestion class="w-16 h-16 mb-4 text-muted-foreground/50" />
            <p>暂无日志数据</p>
          </div>
          <div v-else class="relative pb-20">
            <pre
              ref="logPreElement"
              class="language-log m-0 p-4 pb-12 overflow-visible"
            ><code v-html="highlightedContent"></code></pre>
            <!-- 添加额外的底部空间 -->
            <div class="h-20 w-full"></div>
          </div>
        </div>
      </div>

      <div class="mt-2 flex justify-between items-center text-xs text-muted-foreground">
        <!-- 训练状态展示区域 -->
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-1">
            <span class="font-medium">当前轮次:</span>
            <span>{{ currentEpoch }}{{ totalEpochs > 0 ? `/${totalEpochs}` : '' }}</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="font-medium">训练损失:</span>
            <span class="text-blue-600">{{ currentLoss }}</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="font-medium">训练进度:</span>
            <span class="text-green-600">{{ currentTrainingProgress.toFixed(2) }}%</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="font-medium">已完成:</span>
            <span class="text-success-foreground">{{ taskProcessState.completedSamples }}</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="font-medium">已失败:</span>
            <span class="text-destructive-foreground">{{ taskProcessState.failedSamples }}</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="font-medium">剩余:</span>
            <span class="text-warning-foreground">{{ remainingSamples }}</span>
          </div>
        </div>
        <div>
          <span v-if="filteredLogLines.length !== logLines.length">
            显示 {{ filteredLogLines.length }}/{{ logLines.length }} 行
          </span>
          <span v-else>共 {{ logLines.length }} 行</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              class="h-7 w-7"
              title="滚动到顶部"
              @click="handleScrollToTop"
            >
              <ChevronUp class="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              class="h-7 w-7"
              title="滚动到底部"
              @click="handleScrollToBottom"
            >
              <ChevronDown class="h-4 w-4" />
            </Button>
          </div>
          <Checkbox id="autoScroll" v-model:checked="autoScroll" />
          <label for="autoScroll" class="cursor-pointer">自动滚动</label>
          <Button
            variant="ghost"
            size="sm"
            class="ml-2 h-7"
            :disabled="isLoading || !logContent"
            @click="loadMoreLogs"
          >
            <span>加载更多</span>
            <ChevronDown class="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { Badge } from '@renderer/components/ui/badge'
import { Button } from '@renderer/components/ui/button'
import { Checkbox } from '@renderer/components/ui/checkbox'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { inputParse } from '@renderer/utils/rpcParser'
import { encryptAndSaveFile } from '@renderer/utils/utils'
import {
  ChevronDown,
  ChevronUp,
  Download,
  FileQuestion,
  FileText,
  Loader2,
  RefreshCw,
  Search,
} from 'lucide-vue-next'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { toast } from 'vue-sonner'

// 导入Prism.js及主题样式
import Prism from 'prismjs'
import 'prismjs/themes/prism-tomorrow.css'
// 添加自定义语言定义
Prism.languages.log = {
  info: {
    pattern: /.*INFO.*/,
    greedy: true,
  },
  error: {
    pattern: /.*ERROR.*|.*FAILED.*/,
    greedy: true,
  },
  warning: {
    pattern: /.*WARNING.*|.*WARN.*/,
    greedy: true,
  },
  epoch: {
    pattern: /.*Epoch.*|.*epoch.*/,
    greedy: true,
  },
  loss: {
    pattern: /.*loss.*|.*Loss.*/,
    greedy: true,
  },
}

const props = defineProps<{
  isOpen: boolean
  taskId: string
  serverId?: string
  totalSamples: number
  trainingProgress?: number
}>()

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
}>()

// 状态管理
const taskService = createTaskService()
const logContainer = ref<HTMLElement | null>(null)
const logPreElement = ref<HTMLElement | null>(null)
const isLoading = ref(false)
const logContent = ref('')
const searchQuery = ref('')
const filterType = ref('all')
const autoScroll = ref(true)
const startLine = ref(1)
const lastLineIndex = ref(0)
const batchSize = 500 // 每次加载的行数
const userScrolled = ref(false) // 用户是否手动滚动

// 训练状态
const currentEpoch = ref(0)
const totalEpochs = ref(0)
const currentLoss = ref('--')
const localTrainingProgress = ref(0)

// 任务处理状态 - 参考 HighpowerBatteryModelCalibration
const taskProcessState = ref({
  completedSamples: 0,
  failedSamples: 0,
  currentProcessedFiles: 0,
})

// 使用传入的训练进度，如果没有则使用本地解析的进度
const currentTrainingProgress = computed(() => {
  return props.trainingProgress !== undefined ? props.trainingProgress : localTrainingProgress.value
})

// 计算剩余数量
const remainingSamples = computed(() => {
  const { currentProcessedFiles, completedSamples, failedSamples } = taskProcessState.value

  // 如果文件还在处理中（当前处理文件数 < 总文件数）
  if (currentProcessedFiles < props.totalSamples) {
    return Math.max(0, props.totalSamples - currentProcessedFiles)
  }

  // 如果所有文件都已处理完成，检查是否在训练阶段
  if (currentProcessedFiles >= props.totalSamples) {
    // 如果有epoch信息，显示剩余epoch数
    if (currentEpoch.value > 0 && totalEpochs.value > 0) {
      return Math.max(0, totalEpochs.value - currentEpoch.value)
    }

    // 否则显示文件处理的剩余（通常为0）
    return Math.max(0, props.totalSamples - completedSamples - failedSamples)
  }

  // 默认情况
  return Math.max(0, props.totalSamples - completedSamples - failedSamples)
})

// 更新弹窗状态
const updateOpen = (value: boolean) => {
  emit('update:isOpen', value)
  if (value && props.taskId) {
    startLine.value = 1
    lastLineIndex.value = 0
    logContent.value = ''
    loadLogs()
  }
}

// 日志行处理
const logLines = computed(() => {
  if (!logContent.value) return []
  return logContent.value.split('\n')
})

// 筛选日志行
const filteredLogLines = computed(() => {
  let filtered = logLines.value

  // 按搜索查询筛选
  if (searchQuery.value) {
    filtered = filtered.filter((line) =>
      line.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  }

  // 按类型筛选
  if (filterType.value !== 'all') {
    const filterMap = {
      info: 'INFO',
      error: 'ERROR',
      epoch: 'Epoch',
    }

    filtered = filtered.filter((line) =>
      line.includes(filterMap[filterType.value as keyof typeof filterMap]),
    )
  }

  return filtered
})

// 高亮处理后的内容
const highlightedContent = computed(() => {
  if (!filteredLogLines.value.length) return ''

  // 使用Prism.js处理高亮
  const content = filteredLogLines.value.join('\n')

  try {
    return Prism.highlight(content, Prism.languages.log, 'log')
  } catch (error) {
    console.error('日志高亮处理错误:', error)
    return content
  }
})

// 加载日志数据
const loadLogs = async (append = false) => {
  if (!props.taskId || isLoading.value) return

  // 检查服务器ID是否存在
  if (!props.serverId) {
    toast.error('无法获取日志', {
      description: '服务器ID不存在，请确保任务已成功提交',
    })
    return
  }

  isLoading.value = true

  try {
    const currentStartLine = append ? lastLineIndex.value + 1 : startLine.value

    // 根据用户提供的参数格式构造请求
    const keyValuePairs = {
      log_task_id: props.taskId,
      start_line: currentStartLine.toString(),
    }

    const keyTypePairs = {
      log_task_id: 'String',
      start_line: 'Int32',
    }

    const paramData = inputParse(keyValuePairs, keyTypePairs)

    // 使用 getTrainLog 服务名
    const res = await taskService.callTask('getTrainLog', props.serverId || '', paramData)

    if (res.status === 'Success') {
      // 解析响应结果
      let logs = ''
      let newLastLineIndex = 0

      try {
        // 处理三层嵌套的JSON结构
        if (typeof res.result === 'string') {
          // 第一层解析
          const firstLayer = JSON.parse(res.result)
          console.log(object)

          if (firstLayer.values && typeof firstLayer.values.result === 'string') {
            // 第二层解析
            const secondLayer = JSON.parse(firstLayer.values.result)

            logs = secondLayer.logs || ''
            newLastLineIndex = parseInt(secondLayer.last_line_index || '0')

            // 解析训练状态信息
            parseTrainingStatus(logs)
          } else {
            // 兼容旧格式
            logs = firstLayer.logs || res.result
            newLastLineIndex = parseInt(firstLayer.last_line_index || '0')
            parseTrainingStatus(logs)
          }
        } else if (res.result && typeof res.result === 'object') {
          // 如果result已经是对象
          logs = (res.result as any).logs || ''
          newLastLineIndex = parseInt((res.result as any).last_line_index || '0')
          parseTrainingStatus(logs)
        } else {
          logs = res.result || ''
          parseTrainingStatus(logs)
        }

        // 更新最后一行行号
        if (newLastLineIndex > 0) {
          lastLineIndex.value = newLastLineIndex
        }
      } catch (e) {
        console.error('解析日志结果失败:', e)
        logs = res.result || ''
        parseTrainingStatus(logs)
      }

      if (append) {
        if (logs.trim()) {
          logContent.value += '\n' + logs
          toast.success(`已加载更多日志 (${logs.split('\n').length} 行)`, {
            duration: 2000,
          })
        } else {
          toast.info('没有更多日志了', {
            duration: 2000,
          })
        }
      } else {
        // 只有当 logs 不为空时才设置内容，避免显示空日志
        if (logs.trim()) {
          logContent.value = logs
        } else {
          // 如果日志为空，保持 logContent 为空字符串，这样会显示"暂无日志数据"
          logContent.value = ''
        }
      }

      // 刷新语法高亮
      await nextTick()

      if (autoScroll.value && !append) {
        scrollToBottom()
      }
    } else {
      toast.error('获取训练日志失败', {
        description: res.message || '未知错误',
      })
    }
  } catch (error: any) {
    toast.error('获取训练日志失败', {
      description: error.message || '未知错误',
    })
  } finally {
    isLoading.value = false
  }

  // 返回Promise以便链式调用
  return Promise.resolve()
}

// 解析训练状态信息
const parseTrainingStatus = (logs: string) => {
  if (!logs) return

  const lines = logs.split('\n')
  let totalFiles = 0
  let processedFiles = 0
  let failedFiles = 0
  let maxProcessedFiles = 0

  // 解析最新的训练信息
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]

    // 解析总文件数
    const totalFilesMatch = line.match(/总文件数:\s*(\d+)/)
    if (totalFilesMatch) {
      totalFiles = parseInt(totalFilesMatch[1])
    }

    // 解析处理进度 - 匹配各种处理阶段
    const progressMatch = line.match(/处理(?:训练集|验证集|测试集|支持集)数据:.*?\((\d+)\/(\d+)\)/)
    if (progressMatch) {
      const currentFile = parseInt(progressMatch[1])
      const totalFileCount = parseInt(progressMatch[2])

      // 更新最大处理文件数和总文件数
      maxProcessedFiles = Math.max(maxProcessedFiles, currentFile)
      totalFiles = totalFileCount
    }

    // 解析轮次信息
    const epochMatch = line.match(/Epoch\s+(\d+)\/(\d+)/)
    if (epochMatch) {
      currentEpoch.value = parseInt(epochMatch[1])
      totalEpochs.value = parseInt(epochMatch[2])
      // 基于epoch计算总体进度
      const epochProgress = (currentEpoch.value / totalEpochs.value) * 100
      // 如果有文件处理进度，结合计算
      if (totalFiles > 0) {
        const fileProgress = (maxProcessedFiles / totalFiles) * 100
        // 可以根据训练阶段调整权重
        localTrainingProgress.value = Math.min(100, fileProgress * 0.7 + epochProgress * 0.3)
      } else {
        localTrainingProgress.value = epochProgress
      }
    }

    // 解析损失信息 - 匹配训练损失
    const lossMatch = line.match(/训练\s*-\s*Loss:\s*([\d.]+)/)
    if (lossMatch) {
      currentLoss.value = parseFloat(lossMatch[1]).toFixed(6)
    }

    // 统计失败的文件
    if (line.includes('[ERROR]') && line.includes('处理后没有有效样本')) {
      failedFiles++
    }
  }

  // 更新任务处理状态
  processedFiles = maxProcessedFiles
  taskProcessState.value.currentProcessedFiles = maxProcessedFiles
  taskProcessState.value.completedSamples = Math.max(0, processedFiles - failedFiles)
  taskProcessState.value.failedSamples = failedFiles
}

// 加载更多日志
const loadMoreLogs = () => {
  // 记住当前滚动位置
  const currentScrollPosition = logContainer.value?.scrollTop || 0

  // 临时禁用自动滚动
  const wasAutoScrollEnabled = autoScroll.value
  autoScroll.value = false

  // 加载更多日志
  loadLogs(true).then(() => {
    // 加载完成后恢复自动滚动状态
    setTimeout(() => {
      if (wasAutoScrollEnabled) {
        autoScroll.value = true
        scrollToBottom()
      } else {
        // 如果之前没有启用自动滚动，则保持当前滚动位置
        if (logContainer.value) {
          logContainer.value.scrollTop = currentScrollPosition
        }
      }
    }, 100)
  })
}

// 刷新日志
const handleRefresh = () => {
  startLine.value = 1
  lastLineIndex.value = 0
  logContent.value = ''
  loadLogs()
}

// 导出日志
const handleExport = async () => {
  if (!logContent.value) return

  try {
    const { success, filePath, error } = await encryptAndSaveFile({
      jsonData: logContent.value,
      fileType: 'log',
      defaultPath: `train-log-${props.taskId}.log`,
      title: '导出训练日志',
      filters: [
        { name: '日志文件', extensions: ['log'] },
        { name: '所有文件', extensions: ['*'] },
      ],
    })

    if (success) {
      toast.success('训练日志导出成功', {
        description: `文件已保存至: ${filePath}`,
      })
    } else {
      toast.error('训练日志导出失败', {
        description: error || '未知错误',
      })
    }
  } catch (error: any) {
    toast.error('训练日志导出失败', {
      description: error.message || '未知错误',
    })
  }
}

// 处理滚动事件
const handleScroll = () => {
  if (!logContainer.value) return

  const { scrollTop, scrollHeight, clientHeight } = logContainer.value
  const distanceToBottom = scrollHeight - scrollTop - clientHeight
  const isAtBottom = distanceToBottom < 10

  // 如果用户滚动到底部，重新启用自动滚动
  if (isAtBottom) {
    userScrolled.value = false
    if (!autoScroll.value) {
      autoScroll.value = true
    }
  }
  // 如果用户向上滚动，禁用自动滚动
  else if (autoScroll.value && distanceToBottom > 50) {
    userScrolled.value = true
    autoScroll.value = false
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (!logContainer.value) return

  // 延迟执行滚动，确保DOM已更新
  setTimeout(() => {
    if (logContainer.value) {
      try {
        // 强制滚动到最底部
        logContainer.value.scrollTop = logContainer.value.scrollHeight

        // 再次确认是否真的滚动到底部
        setTimeout(() => {
          if (logContainer.value) {
            const { scrollTop, scrollHeight, clientHeight } = logContainer.value
            const distanceToBottom = scrollHeight - scrollTop - clientHeight

            if (distanceToBottom > 10) {
              logContainer.value.scrollTop = scrollHeight
            }
          }
        }, 50)
      } catch (error) {
        console.error('滚动错误:', error)
      }
    }
  }, 100)
}

// 滚动到顶部
const scrollToTop = () => {
  if (!logContainer.value) return

  logContainer.value.scrollTop = 0
  // 禁用自动滚动
  autoScroll.value = false
  userScrolled.value = true

  toast.success('已滚动到顶部', {
    duration: 1000,
  })
}

// 处理滚动到底部按钮点击
const handleScrollToBottom = () => {
  // 启用自动滚动
  autoScroll.value = true
  userScrolled.value = false

  // 执行滚动
  scrollToBottom()

  toast.success('已滚动到底部', {
    duration: 1000,
  })
}

// 处理滚动到顶部按钮点击
const handleScrollToTop = () => {
  scrollToTop()
}

// 监听日志内容变化，自动滚动
watch(logContent, () => {
  if (autoScroll.value) {
    // 强制重置用户滚动状态，确保可以滚动
    userScrolled.value = false

    nextTick(() => {
      scrollToBottom()
    })
  }
})

// 监听自动滚动状态变化
watch(autoScroll, (newValue) => {
  if (newValue) {
    userScrolled.value = false
    nextTick(() => {
      scrollToBottom()
    })
  }
})

// 监听筛选条件变化，重置滚动状态
watch([searchQuery, filterType], () => {
  userScrolled.value = false
  nextTick(() => {
    if (autoScroll.value) {
      scrollToBottom()
    }
  })
})

// 监听对话框状态和任务ID变化
watch(
  () => [props.isOpen, props.taskId],
  ([isOpen, taskId], [, oldTaskId]) => {
    // 当任务ID变化时，重置起始行和日志内容
    if (taskId !== oldTaskId) {
      startLine.value = 1
      lastLineIndex.value = 0
      logContent.value = ''
    }

    if (isOpen && taskId) {
      loadLogs()
    }
  },
)

// 初始加载
onMounted(() => {
  if (props.isOpen && props.taskId) {
    loadLogs()
  }
})
</script>

<style scoped>
.code-container {
  font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  letter-spacing: -0.3px;
  height: 100%;
  max-height: 60vh;
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  padding-bottom: 20px; /* 添加底部内边距 */
}

.code-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-container::-webkit-scrollbar-track {
  background: transparent;
}

.code-container::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 4px;
}

.code-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

:deep(pre[class*='language-']) {
  margin: 0;
  padding: 1rem;
  padding-bottom: 3rem; /* 增加底部内边距 */
  background-color: transparent !important;
  overflow: visible;
  height: auto;
  min-height: 100%;
  box-sizing: border-box;
  position: relative; /* 添加相对定位 */
}

:deep(code) {
  white-space: pre-wrap;
  word-break: break-word;
  display: block;
}

/* 自定义语法高亮颜色 */
:deep(.token.info) {
  color: #10b981 !important;
}

:deep(.token.error) {
  color: #ef4444 !important;
}

:deep(.token.warning) {
  color: #f59e0b !important;
  font-weight: bold;
}

:deep(.token.epoch) {
  color: #3b82f6 !important;
  font-weight: bold;
}

:deep(.token.loss) {
  color: #8b5cf6 !important;
}

/* 默认文本颜色 - 将灰色文本改为黑色 */
:deep(code[class*='language-']),
:deep(pre[class*='language-']) {
  color: #000000 !important; /* 亮色模式下为黑色 */
}

/* 暗黑模式下文本颜色 */
.dark :deep(code[class*='language-']),
.dark :deep(pre[class*='language-']) {
  color: #ffffff !important; /* 暗黑模式下为白色 */
}

/* 状态颜色样式 */
.text-success-foreground {
  color: hsl(142.1 76.2% 36.3%);
}
.text-destructive-foreground {
  color: hsl(0 84.2% 60.2%);
}
.text-warning-foreground {
  color: hsl(32.1 94.6% 44.3%);
}
</style>
